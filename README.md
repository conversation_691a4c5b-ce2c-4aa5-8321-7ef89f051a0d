# Qt TabWidget 标签页隐藏解决方案

由于 `tabBar()->setTabVisible()` 方法在较旧的Qt版本中不存在，本项目提供了多种兼容的解决方案来实现标签页的隐藏功能。

## 问题背景

原始代码尝试使用 `tabBar()->setTabVisible(i, shouldBeVisible)` 来隐藏标签页，但这个方法只在Qt 5.15+版本中可用。为了兼容更多Qt版本，我们提供了以下几种替代方案。

## 解决方案

### 方案1：移除/插入标签页方法 (`handleLevel`)
- **优点**：完全隐藏标签页，包括标签头
- **缺点**：性能开销大，会丢失标签页状态，可能影响当前选中的标签页
- **适用场景**：标签页数量少且不频繁切换的情况

### 方案2：样式表方法 (`handleLevelStyleSheet`)
- **优点**：通过CSS隐藏标签头，保持内容区域隐藏
- **缺点**：依赖样式表，可能与现有样式冲突
- **适用场景**：对视觉效果要求高的情况

### 方案3：自定义TabBar方法 (`handleLevelCustomTabBar`)
- **优点**：完全控制标签页显示，性能好
- **缺点**：需要额外的类，实现复杂
- **适用场景**：需要完全隐藏标签头且性能要求高的情况

### 方案4：简单方法 (`handleLevelSimple`) - **推荐**
- **优点**：简单可靠，兼容所有Qt版本，不会破坏现有功能
- **缺点**：标签头仍然可见（但会变灰显示禁用状态）
- **适用场景**：大多数情况，特别是只需要功能性隐藏的场景

## 使用方法

### 1. 基本使用

```cpp
CMvTabWidgetExe* tabWidget = new CMvTabWidgetExe();

// 添加标签页
tabWidget->addTab(new QWidget(), "Tab 1");
tabWidget->addTab(new QWidget(), "Tab 2");
tabWidget->addTab(new QWidget(), "Tab 3");

// 设置权限级别信息
// 格式："可见级别-启用级别$-可见级别-启用级别$-..."
tabWidget->setProperty("allTabLevelInfo", "5-3$-8-6$-2-1");

// 应用权限级别（推荐使用简单方法）
tabWidget->handleLevelSimple(4);
```

### 2. 权限级别信息格式

权限信息字符串格式：`"可见级别-启用级别$-可见级别-启用级别$-..."`

例如：`"5-3$-8-6$-2-1"` 表示：
- 第1个标签页：级别5可见，级别3可启用
- 第2个标签页：级别8可见，级别6可启用
- 第3个标签页：级别2可见，级别1可启用

### 3. 动态权限变化

```cpp
// 普通用户（级别1）
tabWidget->handleLevelSimple(1);

// 高级用户（级别5）
tabWidget->handleLevelSimple(5);

// 管理员（级别10）
tabWidget->handleLevelSimple(10);
```

## 编译和运行

### 使用CMake编译

```bash
mkdir build
cd build
cmake ..
cmake --build .
```

### 运行测试程序

```bash
./bin/TabHidingTest
```

测试程序提供了一个图形界面，可以：
- 调整用户权限级别（1-10）
- 选择不同的隐藏方法
- 实时查看标签页状态变化

## 文件说明

- `CMvTabWidgetExe_fixed.cpp` - 主要实现文件，包含所有4种方案
- `CMvTabWidgetExe.h` - 头文件声明
- `CustomTabBar.h/cpp` - 自定义TabBar类（方案3使用）
- `test_tab_hiding.cpp` - 测试程序
- `usage_example.cpp` - 使用示例代码
- `CMakeLists.txt` - CMake构建文件

## 兼容性

- **Qt版本**：支持Qt 5.6+和Qt 6.x
- **编译器**：支持MSVC、GCC、Clang
- **平台**：Windows、Linux、macOS

## 推荐使用

对于大多数应用场景，推荐使用**方案4（简单方法）**：

```cpp
tabWidget->handleLevelSimple(userLevel);
```

这种方法：
- 兼容所有Qt版本
- 实现简单可靠
- 不会破坏现有功能
- 性能开销最小

如果必须完全隐藏标签头，可以考虑使用**方案3（自定义TabBar）**。

## 注意事项

1. 在调用隐藏方法之前，确保已经设置了 `allTabLevelInfo` 属性
2. 权限级别数字越小表示权限越低
3. 如果标签页数量与权限信息不匹配，会自动进行边界检查
4. 建议在权限变化时重新调用相应的处理方法
