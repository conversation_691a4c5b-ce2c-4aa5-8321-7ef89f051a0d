// 修复后的 handleLevel 函数实现 - 方案1：移除/插入标签页方法
void CMvTabWidgetExe::handleLevelRemoveInsert(int level)
{
    QString str = this->property("allTabLevelInfo").toString();
    QStringList list = str.split("$-");
    int nSize = list.size();

    // 存储标签页信息，用于重新插入
    QList<QWidget*> widgets;
    QStringList tabTexts;
    QList<QIcon> tabIcons;
    QList<bool> tabVisibility;
    QList<bool> tabEnabled;

    // 收集所有标签页信息
    for (int i = 0; i < this->count(); ++i)
    {
        widgets.append(this->widget(i));
        tabTexts.append(this->tabText(i));
        tabIcons.append(this->tabIcon(i));
    }

    // 计算每个标签页的可见性和启用状态
    for (int i = 0; i < nSize && i < widgets.size(); ++i)
    {
        int visibleLevel = 10;
        int enabledLevel = 10;
        QStringList levelList = list[i].split("-");
        if (levelList.size() == 2)
        {
            visibleLevel = levelList[0].toInt();
            enabledLevel = levelList[1].toInt();
        }

        bool shouldBeVisible = (level <= visibleLevel);
        bool shouldBeEnabled = (level <= enabledLevel);

        tabVisibility.append(shouldBeVisible);
        tabEnabled.append(shouldBeEnabled);
    }

    // 清空所有标签页
    while (this->count() > 0)
    {
        this->removeTab(0);
    }

    // 重新添加可见的标签页
    for (int i = 0; i < widgets.size() && i < tabVisibility.size(); ++i)
    {
        if (tabVisibility[i])
        {
            int newIndex = this->addTab(widgets[i], tabIcons[i], tabTexts[i]);
            this->setTabEnabled(newIndex, tabEnabled[i]);
        }
    }
}

// 方案2：使用样式表隐藏标签页
void CMvTabWidgetExe::handleLevelStyleSheet(int level)
{
    QString str = this->property("allTabLevelInfo").toString();
    QStringList list = str.split("$-");
    int nSize = list.size();

    QString styleSheet = "QTabWidget::pane { border: 1px solid #C0C0C0; }\n";

    for (int i = 0; i < nSize && i < this->count(); ++i)
    {
        int visibleLevel = 10;
        int enabledLevel = 10;
        QStringList levelList = list[i].split("-");
        if (levelList.size() == 2)
        {
            visibleLevel = levelList[0].toInt();
            enabledLevel = levelList[1].toInt();
        }

        bool shouldBeVisible = (level <= visibleLevel);
        bool shouldBeEnabled = (level <= enabledLevel);

        // 使用样式表隐藏标签页
        if (!shouldBeVisible)
        {
            styleSheet += QString("QTabBar::tab:nth-child(%1) { width: 0px; height: 0px; margin: 0px; padding: 0px; border: none; }\n").arg(i + 1);
        }

        // 设置启用状态
        this->setTabEnabled(i, shouldBeEnabled);

        // 隐藏内容区域
        if (this->widget(i))
        {
            this->widget(i)->setVisible(shouldBeVisible);
        }
    }

    this->setStyleSheet(styleSheet);
}

// 方案3：使用自定义TabBar类
void CMvTabWidgetExe::handleLevelCustomTabBar(int level)
{
    QString str = this->property("allTabLevelInfo").toString();
    QStringList list = str.split("$-");
    int nSize = list.size();

    // 存储隐藏状态
    QList<bool> hiddenTabs;

    for (int i = 0; i < nSize && i < this->count(); ++i)
    {
        int visibleLevel = 10;
        int enabledLevel = 10;
        QStringList levelList = list[i].split("-");
        if (levelList.size() == 2)
        {
            visibleLevel = levelList[0].toInt();
            enabledLevel = levelList[1].toInt();
        }

        bool shouldBeVisible = (level <= visibleLevel);
        bool shouldBeEnabled = (level <= enabledLevel);

        hiddenTabs.append(!shouldBeVisible);

        // 设置启用状态
        this->setTabEnabled(i, shouldBeEnabled);

        // 隐藏内容区域
        if (this->widget(i))
        {
            this->widget(i)->setVisible(shouldBeVisible);
        }
    }

    // 如果使用自定义TabBar，需要设置隐藏状态
    if (CustomTabBar* customBar = qobject_cast<CustomTabBar*>(this->tabBar()))
    {
        customBar->setHiddenTabs(hiddenTabs);
    }
}

// 方案4：最简单的方法 - 仅隐藏内容，保留标签头但禁用
void CMvTabWidgetExe::handleLevelSimple(int level)
{
    QString str = this->property("allTabLevelInfo").toString();
    QStringList list = str.split("$-");
    int nSize = list.size();

    for (int i = 0; i < nSize && i < this->count(); ++i)
    {
        int visibleLevel = 10;
        int enabledLevel = 10;
        QStringList levelList = list[i].split("-");
        if (levelList.size() == 2)
        {
            visibleLevel = levelList[0].toInt();
            enabledLevel = levelList[1].toInt();
        }

        bool shouldBeVisible = (level <= visibleLevel);
        bool shouldBeEnabled = (level <= enabledLevel);

        // 设置标签页启用状态（这会让标签头变灰）
        this->setTabEnabled(i, shouldBeVisible && shouldBeEnabled);

        // 隐藏/显示标签页内容
        if (this->widget(i))
        {
            this->widget(i)->setVisible(shouldBeVisible);
        }
    }
}

/*
各种方案的优缺点比较：

方案1 - 移除/插入标签页：
优点：完全隐藏标签页，包括标签头
缺点：性能开销大，会丢失标签页状态，可能影响当前选中的标签页

方案2 - 样式表方法：
优点：通过CSS隐藏标签头，保持内容区域隐藏
缺点：依赖样式表，可能与现有样式冲突

方案3 - 自定义TabBar：
优点：完全控制标签页显示，性能好
缺点：需要额外的类，实现复杂

方案4 - 简单方法（推荐）：
优点：简单可靠，兼容所有Qt版本，不会破坏现有功能
缺点：标签头仍然可见（但会变灰显示禁用状态）

使用建议：
- 如果只需要功能性隐藏，推荐使用方案4（handleLevelSimple）
- 如果必须完全隐藏标签头，推荐使用方案3（自定义TabBar）
- 方案1适合标签页数量少且不频繁切换的情况
- 方案2适合对视觉效果要求高的情况

使用示例：
- 如果 allTabLevelInfo = "5-3$-8-6$-2-1"
- 当 level = 4 时：
  - 第0个标签页：visibleLevel=5, enabledLevel=3 → 可见但禁用
  - 第1个标签页：visibleLevel=8, enabledLevel=6 → 可见且启用
  - 第2个标签页：visibleLevel=2, enabledLevel=1 → 隐藏（标签头禁用，内容隐藏）
*/
