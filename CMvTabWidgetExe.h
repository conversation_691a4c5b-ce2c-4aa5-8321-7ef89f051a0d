#ifndef CMVTABWIDGETEXE_H
#define CMVTABWIDGETEXE_H

#include <QTabWidget>
#include <QWidget>
#include <QString>
#include <QStringList>
#include <QIcon>
#include <QList>

// 前向声明
class CustomTabBar;

class CMvTabWidgetExe : public QTabWidget
{
    Q_OBJECT

public:
    explicit CMvTabWidgetExe(QWidget *parent = nullptr);
    virtual ~CMvTabWidgetExe();

    // 权限级别控制方法 - 多种实现方案
    
    /**
     * 方案1：移除/插入标签页方法
     * 优点：完全隐藏标签页，包括标签头
     * 缺点：性能开销大，会丢失标签页状态
     * 适用：标签页数量少且不频繁切换的情况
     */
    void handleLevel(int level);
    
    /**
     * 方案2：使用样式表隐藏标签页
     * 优点：通过CSS隐藏标签头，保持内容区域隐藏
     * 缺点：依赖样式表，可能与现有样式冲突
     * 适用：对视觉效果要求高的情况
     */
    void handleLevelStyleSheet(int level);
    
    /**
     * 方案3：使用自定义TabBar类
     * 优点：完全控制标签页显示，性能好
     * 缺点：需要额外的类，实现复杂
     * 适用：需要完全隐藏标签头且性能要求高的情况
     */
    void handleLevelCustomTabBar(int level);
    
    /**
     * 方案4：简单方法（推荐）
     * 优点：简单可靠，兼容所有Qt版本，不会破坏现有功能
     * 缺点：标签头仍然可见（但会变灰显示禁用状态）
     * 适用：大多数情况，特别是只需要功能性隐藏的场景
     */
    void handleLevelSimple(int level);

    // 辅助方法
    void setTabLevelInfo(const QString& levelInfo);
    QString getTabLevelInfo() const;

protected:
    // 可以重写这些方法来自定义行为
    virtual void onTabVisibilityChanged(int index, bool visible);
    virtual void onTabEnabledChanged(int index, bool enabled);

private:
    // 解析级别信息的辅助方法
    void parseLevelInfo(const QString& levelInfo, int index, int& visibleLevel, int& enabledLevel);
    
    // 存储原始标签页信息（用于方案1）
    struct TabInfo {
        QWidget* widget;
        QString text;
        QIcon icon;
        QString toolTip;
        bool enabled;
    };
    
    QList<TabInfo> m_originalTabs;  // 存储原始标签页信息
    bool m_isUpdating;              // 防止递归更新的标志
};

#endif // CMVTABWIDGETEXE_H
